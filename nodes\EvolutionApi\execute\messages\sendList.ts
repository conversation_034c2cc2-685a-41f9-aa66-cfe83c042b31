import {
	IExecuteFunctions,
	IRequestOptions,
	IHttpRequestMethods,
	NodeOperationError,
} from 'n8n-workflow';
import { evolutionRequest } from '../evolutionRequest';

export async function sendList(ef: IExecuteFunctions) {
	try {
		// Parâmetros obrigatórios
		const instanceName = ef.getNodeParameter('instanceName', 0);
		const remoteJid = ef.getNodeParameter('remoteJid', 0);
		const title = ef.getNodeParameter('title', 0);
		const description = ef.getNodeParameter('description', 0);
		const buttonText = ef.getNodeParameter('buttonText', 0);
		const sections = ef.getNodeParameter('sections.sectionValues', 0) as {
			title: string;
			rows: {
				rowValues: {
					title: string;
					description?: string;
					rowId?: string;
				}[];
			};
		}[];

		// Section validation
		if (!Array.isArray(sections) || sections.length === 0) {
			const errorData = {
				success: false,
				error: {
					message: 'Invalid section list',
					details: 'At least one section with options must be provided',
					code: 'INVALID_SECTIONS',
					timestamp: new Date().toISOString(),
				},
			};
			return {
				json: errorData,
				error: errorData,
			};
		}

		// Additional options
		const options = ef.getNodeParameter('options_message', 0, {}) as {
			footer?: string;
			delay?: number;
			quoted?: {
				messageQuoted: {
					messageId: string;
				};
			};
			mentions?: {
				mentionsSettings: {
					mentionsEveryOne: boolean;
					mentioned?: string;
				};
			};
		};

		const body: any = {
			number: remoteJid,
			title,
			description,
			buttonText,
			footerText: options.footer || '',
			sections: sections.map(section => ({
				title: section.title,
				rows: section.rows.rowValues.map(row => ({
					title: row.title,
					description: row.description || '',
					rowId: row.rowId || `${section.title}_${row.title}`
				}))
			}))
		};

		if (options.delay) body.delay = options.delay;

		if (options.quoted?.messageQuoted?.messageId) {
			body.quoted = {
				key: {
					id: options.quoted.messageQuoted.messageId,
				},
			};
		}

		if (options.mentions?.mentionsSettings) {
			const { mentionsEveryOne, mentioned } = options.mentions.mentionsSettings;

			if (mentionsEveryOne) {
				body.mentionsEveryOne = true;
			} else if (mentioned) {
				const mentionedNumbers = mentioned
					.split(',')
					.map(num => num.trim())
					.map(num => num.includes('@s.whatsapp.net') ? num : `${num}@s.whatsapp.net`);

				body.mentioned = mentionedNumbers;
			}
		}

		const requestOptions: IRequestOptions = {
			method: 'POST' as IHttpRequestMethods,
			headers: {
				'Content-Type': 'application/json',
			},
			uri: `/message/sendList/${instanceName}`,
			body,
			json: true,
		};

		const response = await evolutionRequest(ef, requestOptions);
		return {
			json: {
				success: true,
				data: response,
			},
		};
	} catch (error) {
		const errorData = {
			success: false,
			error: {
				message: error.message.includes('Could not get parameter')
					? 'Invalid or missing parameters'
					: 'Error sending list',
				details: error.message.includes('Could not get parameter')
					? 'Please check that all required fields have been filled in correctly'
					: error.message,
				code: error.code || 'UNKNOWN_ERROR',
				timestamp: new Date().toISOString(),
			},
		};

		if (!ef.continueOnFail()) {
			throw new NodeOperationError(ef.getNode(), error.message, {
				message: errorData.error.message,
				description: errorData.error.details,
			});
		}

		return {
			json: errorData,
			error: errorData,
		};
	}
}
