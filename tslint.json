{"linterOptions": {"exclude": ["node_modules/**/*"]}, "defaultSeverity": "error", "jsRules": {}, "rules": {"array-type": [true, "array-simple"], "arrow-return-shorthand": true, "ban": [true, {"name": "Array", "message": "tsstyle#array-constructor"}], "ban-types": [true, ["Object", "Use {} instead."], ["String", "Use 'string' instead."], ["Number", "Use 'number' instead."], ["Boolean", "Use 'boolean' instead."]], "class-name": true, "curly": [true, "ignore-same-line"], "forin": true, "jsdoc-format": true, "label-position": true, "indent": [true, "tabs", 2], "member-access": [true, "no-public"], "new-parens": true, "no-angle-bracket-type-assertion": true, "no-any": true, "no-arg": true, "no-conditional-assignment": true, "no-construct": true, "no-debugger": true, "no-default-export": true, "no-duplicate-variable": true, "no-inferrable-types": true, "ordered-imports": [true, {"import-sources-order": "any", "named-imports-order": "case-insensitive"}], "no-namespace": [true, "allow-declarations"], "no-reference": true, "no-string-throw": true, "no-unused-expression": true, "no-var-keyword": true, "object-literal-shorthand": true, "only-arrow-functions": [true, "allow-declarations", "allow-named-functions"], "prefer-const": true, "radix": true, "semicolon": [true, "always", "ignore-bound-class-methods"], "switch-default": true, "trailing-comma": [true, {"multiline": {"objects": "always", "arrays": "always", "functions": "always", "typeLiterals": "ignore"}, "esSpecCompliant": true}], "triple-equals": [true, "allow-null-check"], "use-isnan": true, "quotes": ["error", "single"], "variable-name": [true, "check-format", "ban-keywords", "allow-leading-underscore", "allow-trailing-underscore"]}, "rulesDirectory": []}