Document Note:
This document serves to define Operations that should appear
in each Resource, including the creation of new fields for each Operation

 ██████╗ ██████╗ ███████╗██████╗  █████╗ ████████╗██╗ ██████╗ ███╗   ██╗
██╔═══██╗██╔══██╗██╔════╝██╔══██╗██╔══██╗╚══██╔══╝██║██╔═══██╗████╗  ██║
██║   ██║██████╔╝█████╗  ██████╔╝███████║   ██║   ██║██║   ██║██╔██╗ ██║
██║   ██║██╔═══╝ ██╔══╝  ██╔══██╗██╔══██║   ██║   ██║██║   ██║██║╚██╗██║
╚██████╔╝██║     ███████╗██║  ██║██║  ██║   ██║   ██║╚██████╔╝██║ ╚████║
 ╚═════╝ ╚═╝     ╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝

Operation = Functions/Options of each Resource (Instance, Messages, Integration, Chat, Profile and Group)

"What each Resource will present when selected"

Example:
Instances:
  • Create instance;
  • Create instance with proxy;
  • Connect instance;

Each added Resource will have one of these to list the options/functions of each Resource




	███████╗██╗     ███████╗███╗   ███╗███████╗███╗   ██╗████████╗███████╗
	██╔════╝██║     ██╔════╝████╗ ████║██╔════╝████╗  ██║╚══██╔══╝██╔════╝
	█████╗  ██║     █████╗  ██╔████╔██║█████╗  ██╔██╗ ██║   ██║   ███████╗
	██╔══╝  ██║     ██╔══╝  ██║╚██╔╝██║██╔══╝  ██║╚██╗██║   ██║   ╚════██║
	███████╗███████╗███████╗██║ ╚═╝ ██║███████╗██║ ╚████║   ██║   ███████║
	╚══════╝╚══════╝╚══════╝╚═╝     ╚═╝╚══════╝╚═╝  ╚═══╝   ╚═╝   ╚══════╝

Elements = The fields that appear in each Operation

"Each Operation (e.g.: Send Text Message) has fields that need to be filled to make a request"

Example:
Send Text Message:
  • Instance that will send;
  • RemoteJid of the recipient;
  • Message that will be sent;

