![image](https://github.com/user-attachments/assets/813b7b34-377c-42e8-9f1a-12e27e682c7f)

<p align="center"><br>
Este Community Nodes é uma solução 100% gratuita, criada com o intuito de simplificar e auxiliar toda a comunidade a integrar e utilizar ao máximo os principais recursos oferecidos pela <b>Evolution API v2.2+</b> em seus projetos no N8N. <b>Desenvolvido por OrionDesign.</b>
</p>
<br>
	
<div align="center">
  <img src="https://img.shields.io/badge/dynamic/json?url=https%3A%2F%2Fapi.npmjs.org%2Fdownloads%2Fpoint%2Flast-year%2Fn8n-nodes-evolution-api&query=downloads&style=for-the-badge&label=Total%20de%20Downloads&labelColor=%230d1117&color=%23359514&cacheSeconds=30&link=https%3A%2F%2Fwww.npmjs.com%2Fpackage%2Fn8n-nodes-evolution-api" alt="Dynamic JSON Badge">
</div>
<br>
</p>
<p align="center">
  <a href="https://oriondesign.art.br/whatsapp1"><img src="https://github.com/user-attachments/assets/5a469114-2054-4f01-85b2-51a282518658" alt=”SetupOrion” ></a>     
  <a href="https://oriondesign.art.br/whatsapp2"><img src="https://github.com/user-attachments/assets/3e3580a9-ae8e-4209-84fc-cfc1c03a8f12" alt=”SetupOrion” ></a>     
  <a href="https://oriondesign.art.br/whatsapp3"><img src="https://github.com/user-attachments/assets/91aa7733-c09c-474f-9483-54cb678213d2" alt=”SetupOrion” ></a>
</p>


<h1></h1>

<h3>⚙️ Requisitos</h3>

Para utilizar o nosso **Community Node**, é necessário atender aos seguintes requisitos:  
- **N8N** na versão **1.54.4** ou superior  
- **Evolution API** na versão **2.2.0** ou superior  

<h1></h1>

<h3>📌 Recursos Disponíveis</h3>

<h3>Instância</h3>
🖥️ Este recurso oferece acesso completo às principais funcionalidades relacionadas às instâncias da Evolution API. Ele permite realizar operações essenciais, como criar novas instâncias, conectar-se a elas, obter informações detalhadas, personalizar comportamentos, monitorar presença, reiniciar e até mesmo excluir instâncias de forma prática e eficiente.
<br>
<details>
  <summary><b>Lista de operações</b></summary>
	<details>
  	<summary>   ✅ <b> Criar Instancia</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Gerar Qr-Code</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Buscar Instancia</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Definir Comportamento</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Definir Presença</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Definir Proxy</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Buscar Proxy</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Desconectar WhatsApp</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Deletar Instancia</b></summary>
	</details>
</details>

<h3> Mensagem</h3>
✉️ Este recurso concentra todas as funcionalidades relacionadas ao envio e gerenciamento de mensagens através da Evolution API. Com ele, você pode enviar diversos tipos de conteúdo como textos, imagens, vídeos, áudios, documentos, contatos, listas interativas, botões e até mesmo mensagens PIX. Cada tipo de mensagem possui opções avançadas como delay, mentions, replies e formatações especiais. O recurso foi desenvolvido para proporcionar uma experiência completa de comunicação, permitindo explorar ao máximo os recursos nativos do WhatsApp de forma simples e eficiente.
<br>
<details>
  <summary><b>Lista de operações</b></summary>
	<details>
  	<summary>   ✅ <b> Enviar Texto</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Enviar Imagem</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Enviar Video</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Enviar Audio</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Enviar Documento</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Enviar Enquete</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Enviar Contato</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Enviar Lista</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Enviar Botão</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Enviar Pix</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Enviar Status</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Reagir a Mensagem</b></summary>
	</details>
</details>

<h3>Grupo</h3>
👥 Com este recurso, você conta com um conjunto completo de funcionalidades para gerenciamento de grupos no WhatsApp por meio da Evolution API. Ele abrange desde a criação e administração de grupos até o gerenciamento de participantes, configuração de permissões, links de convite e mensagens temporárias. Tudo foi projetado para oferecer controle eficiente e simplificado na administração de grupos.
<br>
<details>
  <summary><b>Lista de operações</b></summary>
	<details>
  	<summary>   ✅ <b> Criar Grupo</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Atualizar Imagem do Grupo</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Atualizar Nome do Grupo</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Atualizar Descrição do Grupo</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Atualizar Configurações do Grupo</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Atualizar Membros</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Buscar Link de convite do Grupo</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Revogar Link de convite do Grupo</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Enviar Link de Convite do Grupo</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Encontrar Participantes</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Mensagens Temporarias</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Sair do Grupo</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Entrar no Grupo</b></summary>
	</details>
</details>

<h3>Chat</h3>
💬 Este recurso disponibiliza um conjunto abrangente de ferramentas para o gerenciamento de conversas e interações utilizando a Evolution API. Com ele, é possível verificar números, enviar e gerenciar mensagens, manipular arquivos de mídia, controlar status de leitura, administrar contatos e monitorar presença. Todas as operações são desenvolvidas para proporcionar um controle completo e eficiente das comunicações, facilitando a gestão tanto de conversas individuais quanto em grupo.
<br>
<details>
  <summary><b>Lista de operações</b></summary>
	<details>
  	<summary>   ✅ <b> Verificar Numero</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Ler Mensagem</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Gerenciar Arquivoo</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Marcar como Não lido</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Deletar Mensagem</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Buscar Foto de Perfil</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Obter Midia em Base64</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Editar Mensagem</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Enviar Presença</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Bloquear Contato</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Buscar Contatos</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Procurar Mensagens</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Procurar Status</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Procurar Chats</b></summary>
	</details>
</details>

<h3>Evento</h3>
⚡ Este recurso oferece mecanismos avançados para integração e monitoramento em tempo real das atividades da Evolution API. Ele permite configurar e gerenciar Webhooks e RabbitMQ, possibilitando o acompanhamento de eventos como mensagens recebidas, alterações em grupos, status de conexão e muito mais. Essas funcionalidades foram projetadas para garantir uma comunicação ágil e automatizada entre sua aplicação e a API, promovendo respostas imediatas a diferentes eventos do WhatsApp.
<br>
<details>
  <summary><b>Lista de operações</b></summary>
	<details>
  	<summary>   ✅ <b> Webhook</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> RabbitMQ</b></summary>
	</details>
</details>

<h3>Integração</h3>
🔗 Este recurso disponibiliza uma ampla gama de conectores para integrar a Evolution API a diversas plataformas e serviços externos. Entre as possibilidades estão conexões com Chatwoot para atendimento ao cliente, Evolution Bot para automações, Typebot para fluxos conversacionais, além de integrações com Flowise e Dify para soluções de inteligência artificial. Essas integrações ampliam as capacidades da API, permitindo criar soluções robustas e automatizadas para diferentes cenários de negócios.
<br>
<details>
  <summary><b>Lista de operações</b></summary>
	<details>
  	<summary>   ✅ <b> Chatwoot</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Evolution Bot</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Typebot</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Dify</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Floise</b></summary>
	</details>
</details>

<h1></h1>

<h3>🤝 Contribuição</h3>

Contribua para o crescimento deste projeto! Você pode ajudar de diversas formas:  
- **Pull Requests**: Envie melhorias, correções ou novas funcionalidades.  
- **Issues**: Relate problemas ou sugira novas ideias.  
- **Sugestões**: Compartilhe suas opiniões e feedbacks.  
- **Documentação**: Ajude a melhorar ou expandir a documentação existente.  

<h1></h1>

<h3>📌 Principais contribuidores</h3>
<a align="center" href="https://github.com/oriondesign2015/n8n-nodes-evolution-api/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=oriondesign2015/n8n-nodes-evolution-api" />
</a>

<h1></h1>
<a href="https://star-history.com/#oriondesign2015/SetupOrion&Date">
 <picture>
   <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=oriondesign2015/SetupOrion&type=Date&theme=dark" />
   <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=oriondesign2015/SetupOrion&type=Date" />
   <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=oriondesign2015/SetupOrion&type=Date" />
 </picture>
</a>

<h1></h1>
<p align="center">
Desenvolvido com ❤️ por OrionDesign
</p>
